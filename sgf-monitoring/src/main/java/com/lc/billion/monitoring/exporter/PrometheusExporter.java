package com.lc.billion.monitoring.exporter;

import io.micrometer.prometheus.PrometheusMeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;

/**
 * Prometheus指标导出器
 * <p>
 * 提供HTTP端点导出Prometheus格式的指标数据
 * 使用轻量级的HttpServer，避免依赖重型Web框架
 * </p>
 *
 * <AUTHOR>
 */
public class PrometheusExporter {
    
    private static final Logger log = LoggerFactory.getLogger(PrometheusExporter.class);
    
    private final PrometheusMeterRegistry prometheusMeterRegistry;
    private final int port;
    private final String path;
    
    private HttpServer httpServer;
    private ScheduledExecutorService executorService;
    
    public PrometheusExporter(PrometheusMeterRegistry prometheusMeterRegistry, int port, String path) {
        this.prometheusMeterRegistry = prometheusMeterRegistry;
        this.port = port;
        this.path = path;
    }
    
    @PostConstruct
    public void start() {
        try {
            // 创建HTTP服务器
            httpServer = HttpServer.create(new InetSocketAddress(port), 0);
            
            // 创建线程池
            executorService = Executors.newFixedThreadPool(2, r -> {
                Thread thread = new Thread(r, "prometheus-exporter");
                thread.setDaemon(true);
                return thread;
            });
            
            httpServer.setExecutor(executorService);
            
            // 注册指标端点
            httpServer.createContext(path, new MetricsHandler());
            
            // 注册健康检查端点
            httpServer.createContext("/health", new HealthHandler());
            
            // 启动服务器
            httpServer.start();
            
            log.info("Prometheus指标导出器已启动，端口: {}, 路径: {}", port, path);
            log.info("指标访问地址: http://localhost:{}{}", port, path);
            log.info("健康检查地址: http://localhost:{}/health", port);
        } catch (IOException e) {
            log.error("启动Prometheus导出器失败", e);
            throw new RuntimeException("无法启动Prometheus导出器", e);
        }
    }
    
    @PreDestroy
    public void stop() {
        log.info("正在关闭Prometheus指标导出器...");
        
        if (httpServer != null) {
            httpServer.stop(5); // 5秒内停止
        }
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        log.info("Prometheus指标导出器已关闭");
    }
    
    /**
     * 指标处理器
     */
    private class MetricsHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                sendResponse(exchange, 405, "Method Not Allowed");
                return;
            }
            
            try {
                // 获取Prometheus格式的指标数据
                String metricsData = prometheusMeterRegistry.scrape();
                
                // 设置响应头
                exchange.getResponseHeaders().set("Content-Type", "text/plain; version=0.0.4; charset=utf-8");
                exchange.getResponseHeaders().set("Cache-Control", "no-cache, no-store, must-revalidate");
                
                // 发送响应
                sendResponse(exchange, 200, metricsData);
                
                log.debug("返回了 {} 字节的指标数据", metricsData.getBytes(StandardCharsets.UTF_8).length);
            } catch (Exception e) {
                log.error("获取指标数据时发生错误", e);
                sendResponse(exchange, 500, "Internal Server Error: " + e.getMessage());
            }
        }
    }
    
    /**
     * 健康检查处理器
     */
    private class HealthHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                sendResponse(exchange, 405, "Method Not Allowed");
                return;
            }
            
            try {
                // 简单的健康检查
                long meterCount = prometheusMeterRegistry.getMeters().size();
                String healthData = String.format("{\"status\":\"UP\",\"metrics_count\":%d,\"timestamp\":%d}", 
                        meterCount, System.currentTimeMillis());
                
                exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
                sendResponse(exchange, 200, healthData);
            } catch (Exception e) {
                log.error("健康检查时发生错误", e);
                String errorData = String.format("{\"status\":\"DOWN\",\"error\":\"%s\",\"timestamp\":%d}", 
                        e.getMessage(), System.currentTimeMillis());
                exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
                sendResponse(exchange, 503, errorData);
            }
        }
    }
    
    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }
    
    /**
     * 获取服务器端口
     */
    public int getPort() {
        return port;
    }
    
    /**
     * 获取指标路径
     */
    public String getPath() {
        return path;
    }
    
    /**
     * 检查服务器是否正在运行
     */
    public boolean isRunning() {
        return httpServer != null;
    }
} 