package com.lc.billion.monitoring.annotation;

import com.lc.billion.monitoring.config.MonitoringConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 启用监控功能
 * <p>
 * 在Spring配置类上添加此注解以启用监控功能
 * </p>
 *
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(MonitoringConfiguration.class)
public @interface EnableMonitoring {
    
    /**
     * 应用名称，用于指标标签
     */
    String applicationName() default "";
    
    /**
     * 是否启用JVM指标
     */
    boolean enableJvmMetrics() default true;
    
    /**
     * 是否启用WebSocket指标
     */
    boolean enableWebSocketMetrics() default true;
    
    /**
     * 是否启用业务方法指标
     */
    boolean enableMethodMetrics() default true;
} 