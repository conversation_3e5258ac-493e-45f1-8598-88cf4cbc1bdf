package com.lc.billion.monitoring.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 方法调用次数监控注解
 * <p>
 * 用于监控方法调用次数，支持成功/失败分别计数
 * </p>
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Counted {
    
    /**
     * 指标名称
     */
    String name() default "";
    
    /**
     * 指标描述
     */
    String description() default "";
    
    /**
     * 是否记录失败情况
     */
    boolean recordFailuresOnly() default false;
    
    /**
     * 额外的标签
     */
    String[] extraTags() default {};
} 