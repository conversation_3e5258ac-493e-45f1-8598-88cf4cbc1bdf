# SGFçæ§æ¨¡åéç½®

# åºç¨åç§°
monitoring.application.name=icefire-game

# JVMçæ§
monitoring.jvm.enabled=true

# WebSocketçæ§
monitoring.websocket.enabled=true

# ä¸å¡æ¹æ³çæ§
monitoring.method.enabled=true

# Prometheuså¯¼åºå¨éç½®
monitoring.prometheus.enabled=true
monitoring.prometheus.port=9090
monitoring.prometheus.path=/metrics

# æ§è½ä¼åéç½®
monitoring.async.enabled=true
monitoring.async.queue.size=10000
monitoring.async.thread.pool.size=2

# ææ éæ ·éç½®
monitoring.sampling.enabled=false
monitoring.sampling.rate=0.1

# æ¥å¿éç½®
monitoring.logging.level=INFO
monitoring.logging.enabled=true 